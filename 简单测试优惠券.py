"""
简单测试优惠券选择
"""

# 模拟优惠券数据
coupons = [
    {"couponType": "1", "couponTypeName": "23元免单券", "pledgeAmt": 2300},
    {"couponType": "1", "couponTypeName": "20元免单券", "pledgeAmt": 2000},
    {"couponType": "1", "couponTypeName": "2元通用券", "pledgeAmt": 200}
]

def test_selection(order_amt, coupons):
    print(f"订单金额: {order_amt/100:.2f}元")
    
    usable = []
    for c in coupons:
        if c['pledgeAmt'] <= order_amt:
            usable.append(c)
            print(f"  可用: {c['couponTypeName']} - {c['pledgeAmt']/100:.2f}元")
    
    if usable:
        best = max(usable, key=lambda x: x['pledgeAmt'])
        print(f"  选择: {best['couponTypeName']}")
        print(f"  支付: {(order_amt - best['pledgeAmt'])/100:.2f}元")
    else:
        print(f"  无可用优惠券")
    print()

# 测试不同金额
print("智能优惠券选择测试")
print("=" * 30)

test_selection(2600, coupons)  # 26元
test_selection(2000, coupons)  # 20元  
test_selection(1500, coupons)  # 15元
test_selection(500, coupons)   # 5元

print("修复说明:")
print("- 26元订单: 选择23元券，支付3元")
print("- 20元订单: 选择20元券，支付0元 (而不是23元券)")
print("- 15元订单: 选择2元券，支付13元 (而不是浪费大额券)")
print("- 5元订单: 选择2元券，支付3元")
