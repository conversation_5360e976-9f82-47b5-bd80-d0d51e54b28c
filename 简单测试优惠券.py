"""
简单测试优惠券选择
"""

# 模拟完整的优惠券数据（包含你提到的所有券）
coupons = [
    {"couponType": "1", "couponTypeName": "23元免单券", "pledgeAmt": 2300},
    {"couponType": "1", "couponTypeName": "20元免单券", "pledgeAmt": 2000},
    {"couponType": "1", "couponTypeName": "2元通用券", "pledgeAmt": 200},
    {"couponType": "2", "couponTypeName": "8折券", "pledgeAmt": 80, "dimensionList": [{"dimensionKey": "rebate", "dimensionRecordList": [{"dimensionValue": "80"}]}]}
]

def test_selection(order_amt, coupons):
    print(f"订单金额: {order_amt/100:.2f}元")

    usable = []
    for c in coupons:
        coupon_type = c.get('couponType', '')
        coupon_name = c.get('couponTypeName', '未知优惠券')

        if coupon_type == '1':  # 免单券
            pledge_amt = c.get('pledgeAmt', 0)
            if pledge_amt <= order_amt:  # 抵扣金额不超过订单金额
                usable.append({
                    'coupon': c,
                    'deduct_amt': pledge_amt,
                    'final_pay': order_amt - pledge_amt,
                    'name': coupon_name
                })
                print(f"  可用: {coupon_name} - 抵扣{pledge_amt/100:.2f}元")
        elif coupon_type == '2':  # 折扣券
            # 计算折扣后的金额
            discount_rate = 100
            for dimension in c.get('dimensionList', []):
                if dimension.get('dimensionKey') == 'rebate':
                    for record in dimension.get('dimensionRecordList', []):
                        if record.get('dimensionValue'):
                            discount_rate = int(record['dimensionValue'])
                            break

            if discount_rate < 100:
                discounted_amt = order_amt * discount_rate // 100
                deduct_amt = order_amt - discounted_amt
                usable.append({
                    'coupon': c,
                    'deduct_amt': deduct_amt,
                    'final_pay': discounted_amt,
                    'name': f"{coupon_name}({discount_rate}折)"
                })
                print(f"  可用: {coupon_name}({discount_rate}折) - 抵扣{deduct_amt/100:.2f}元")

    if usable:
        # 新的智能选择逻辑：优先选择最接近订单金额的券
        full_coverage = [c for c in usable if c['final_pay'] == 0]
        partial_coverage = [c for c in usable if c['final_pay'] > 0]

        if full_coverage:
            # 有能完全覆盖的券，选择抵扣金额最小的（最接近订单金额）
            best = min(full_coverage, key=lambda x: x['deduct_amt'])
            strategy = "精确匹配"
        else:
            # 没有能完全覆盖的券，选择抵扣金额最大的
            best = max(partial_coverage, key=lambda x: x['deduct_amt'])
            strategy = "最大抵扣"

        print(f"  选择: {best['name']} ({strategy})")
        print(f"  支付: {best['final_pay']/100:.2f}元")
    else:
        print(f"  无可用优惠券")
    print()

# 测试不同金额
print("智能优惠券选择测试")
print("=" * 30)

test_selection(2600, coupons)  # 26元
test_selection(2000, coupons)  # 20元  
test_selection(1500, coupons)  # 15元
test_selection(500, coupons)   # 5元

print("智能选择策略说明:")
print("✅ 精确匹配策略: 当有多个券都能完全覆盖订单时，选择最接近订单金额的券")
print("✅ 最大抵扣策略: 当没有券能完全覆盖订单时，选择抵扣金额最大的券")
print()
print("具体效果:")
print("- 26元订单: 选择23元券，支付3元 (23元券最接近26元)")
print("- 20元订单: 选择20元券，支付0元 (20元券精确匹配，不浪费23元券)")
print("- 15元订单: 选择2元券，支付13元 (只有2元券可用)")
print("- 5元订单: 选择2元券，支付3元 (只有2元券可用)")
print()
print("🎯 核心优势: 避免浪费大额优惠券，为后续更大金额订单保留更好的券")
