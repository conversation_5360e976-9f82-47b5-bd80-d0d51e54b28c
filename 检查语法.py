import ast
import traceback

try:
    with open('顺丰获取图片码授权+打开链接.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 尝试解析语法
    ast.parse(content)
    print("✅ 语法检查通过！")
    
except SyntaxError as e:
    print(f"❌ 语法错误:")
    print(f"   文件: {e.filename}")
    print(f"   行号: {e.lineno}")
    print(f"   列号: {e.offset}")
    print(f"   错误: {e.msg}")
    print(f"   代码: {e.text}")
    
    # 显示错误行附近的代码
    lines = content.split('\n')
    start = max(0, e.lineno - 5)
    end = min(len(lines), e.lineno + 5)
    
    print(f"\n错误行附近的代码:")
    for i in range(start, end):
        marker = ">>> " if i + 1 == e.lineno else "    "
        print(f"{marker}{i+1:4d}: {lines[i]}")
        
except Exception as e:
    print(f"其他错误: {e}")
    traceback.print_exc()
