import time
import re
import os
import json
import requests
import webbrowser
from PIL import ImageGrab, Image
import numpy as np
import cv2
from pyzbar.pyzbar import decode
from urllib.parse import unquote, urlparse, parse_qs
import tkinter as tk
from tkinter import simpledialog, messagebox, ttk
import urllib.parse
import threading
import pyperclip
import win32gui
import win32con
import win32ui
import win32api
from ctypes import windll
import psutil
import pickle
import urllib3
import uuid
import random
import qrcode
from tkinter import Toplevel, Label

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def decode_qr_code_from_image(img):
    try:
        img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
    except Exception as e:
        print(f"图片转换失败: {e}")
        return None
    decoded_objs = decode(img_cv)
    for obj in decoded_objs:
        data = obj.data.decode('utf-8')
        if data.startswith('http'):
            return data
    return None

def generate_qr_code(url, size=(300, 300)):
    """生成二维码图片"""
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(url)
        qr.make(fit=True)

        # 创建二维码图片
        qr_img = qr.make_image(fill_color="black", back_color="white")
        qr_img = qr_img.resize(size, Image.Resampling.LANCZOS)
        return qr_img
    except Exception as e:
        print(f"生成二维码失败: {e}")
        return None

def show_qr_code_window(url, phone=""):
    """显示二维码窗口"""
    try:
        # 生成二维码
        qr_img = generate_qr_code(url)
        if not qr_img:
            return

        # 创建新窗口
        qr_window = Toplevel()
        qr_window.title(f"支付二维码 - {phone}")
        qr_window.geometry("450x500")
        qr_window.resizable(False, False)

        # 设置窗口置顶
        qr_window.attributes('-topmost', True)

        # 转换PIL图片为tkinter可用格式
        from PIL import ImageTk
        qr_photo = ImageTk.PhotoImage(qr_img)

        # 显示二维码
        qr_label = Label(qr_window, image=qr_photo)
        qr_label.image = qr_photo  # 保持引用
        qr_label.pack(pady=10)

        # 显示提示信息
        info_label = Label(qr_window, text=f"手机号: {phone}\n请使用微信扫码支付",
                          font=("Arial", 12), fg="blue")
        info_label.pack(pady=5)

        # 显示URL（可复制）
        url_text = tk.Text(qr_window, height=3, width=50, wrap=tk.WORD)
        url_text.insert(tk.END, url)
        url_text.config(state=tk.DISABLED)
        url_text.pack(pady=5)

        # 按钮框架
        button_frame = tk.Frame(qr_window)
        button_frame.pack(pady=5)

        # 复制URL按钮
        def copy_url():
            pyperclip.copy(url)
            messagebox.showinfo("提示", "支付链接已复制到剪切板")

        copy_btn = tk.Button(button_frame, text="复制支付链接", command=copy_url,
                            bg="#4CAF50", fg="white", font=("Arial", 10))
        copy_btn.pack(side='left', padx=5)

        # 已支付按钮
        def mark_as_paid():
            try:
                # 记录支付状态
                log_payment_result(phone, url, "已支付")

                # 关闭窗口
                qr_window.destroy()

                print(f"✅ 手动标记支付完成 - {phone}")

            except Exception as e:
                print(f"标记支付失败: {e}")
                # 即使出错也关闭窗口
                qr_window.destroy()

        paid_btn = tk.Button(button_frame, text="已支付", command=mark_as_paid,
                            bg="#2196F3", fg="white", font=("Arial", 10, "bold"))
        paid_btn.pack(side='left', padx=5)

        # 关闭按钮
        close_btn = tk.Button(button_frame, text="关闭", command=qr_window.destroy,
                             bg="#f44336", fg="white", font=("Arial", 10))
        close_btn.pack(side='left', padx=5)

        # 居中显示窗口
        qr_window.update_idletasks()
        x = (qr_window.winfo_screenwidth() // 2) - (qr_window.winfo_width() // 2)
        y = (qr_window.winfo_screenheight() // 2) - (qr_window.winfo_height() // 2)
        qr_window.geometry(f"+{x}+{y}")

        print(f"✅ 二维码窗口已显示 - {phone}")

    except Exception as e:
        print(f"显示二维码窗口失败: {e}")
        # 如果二维码显示失败，回退到浏览器打开
        webbrowser.open(url)

def extract_id_from_url(url):
    match = re.search(r'i=([a-zA-Z0-9]+)', url)
    if match:
        return match.group(1)
    return None

def get_url_by_phone(phone, txt_path):
    with open(txt_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line.startswith(phone + "----"):
                return line.strip().split("----", 1)[1]
    return None

def log_payment_result(phone, url, status, amount=None):
    """记录支付结果到文件"""
    try:
        log_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "支付记录.txt")
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")

        if status == "已支付":
            log_line = f"{phone}----{url}----{status}----{timestamp}\n"
        elif status == "自动支付成功":
            log_line = f"{phone}----{url}----{status}----{timestamp}\n"
        elif status == "需要手动支付":
            amount_str = f"{amount/100:.2f}元" if amount else "未知金额"
            log_line = f"{phone}----{url}----{status}({amount_str})----{timestamp}\n"
        elif status == "支付失败":
            log_line = f"{phone}----{url}----{status}----{timestamp}\n"
        else:
            log_line = f"{phone}----{url}----{status}----{timestamp}\n"

        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(log_line)

        print(f"记录已保存: {log_line.strip()}")
    except Exception as e:
        print(f"保存支付记录失败: {e}")

def extract_memberId_mobile(url):
    params = urllib.parse.parse_qs(urllib.parse.urlparse(url).query)
    memberId = params.get('memId', [''])[0]
    mobile = params.get('mobile', [''])[0]
    memberId = urllib.parse.unquote(memberId)
    mobile = urllib.parse.unquote(mobile)
    return memberId, mobile

def get_cookies_and_params_from_url(url):
    session = requests.Session()
    resp = session.get(url, allow_redirects=False, verify=False)
    # 获取所有Set-Cookie
    cookies_dict = session.cookies.get_dict()
    cookies_str = "; ".join([f"{k}={v}" for k, v in cookies_dict.items()])
    # 从Location提取userId
    location = resp.headers.get('Location', '')
    qs = parse_qs(urlparse(location).query)
    memberId = qs.get('userId', [''])[0]
    # 从Set-Cookie获取mobile
    mobile = cookies_dict.get('_login_mobile_', '')
    return cookies_str, memberId, mobile

def get_order_info(uuid):
    """获取订单信息，包括金额和优惠券信息"""
    try:
        import time
        current_time = int(time.time() * 1000)
        # 确保UUID有APP前缀
        if not uuid.startswith('APP'):
            uuid = f"APP{uuid}"
        url = f"https://sfpay.sf-express.com/nextpay/coupons/getWaybills?uuid={uuid}&time={current_time}"

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0",
            "Accept": "application/json, text/plain, */*",
            "Referer": "https://sfpay.sf-express.com/nextpay/html/newcoupon/index88.html",
            "uuid": uuid
        }

        response = requests.get(url, headers=headers, verify=False)
        if response.status_code == 200:
            return response.json()
        return None
    except Exception as e:
        print(f"获取订单信息失败: {e}")
        return None

def select_best_coupon(order_amt, coupons):
    """智能选择最优优惠券

    Args:
        order_amt: 订单金额（分）
        coupons: 优惠券列表

    Returns:
        最优优惠券信息，如果没有找到则返回None
    """
    if not coupons:
        return None

    # 过滤出可用的优惠券（抵扣金额不超过订单金额）
    usable_coupons = []
    for coupon in coupons:
        pledge_amt = coupon.get('pledgeAmt', 0)
        coupon_type = coupon.get('couponType', '')
        coupon_name = coupon.get('couponTypeName', '未知优惠券')

        # 只考虑免单券（couponType为1）和折扣券（couponType为2）
        if coupon_type in ['1', '2']:
            if coupon_type == '1':  # 免单券
                if pledge_amt <= order_amt:  # 抵扣金额不超过订单金额
                    usable_coupons.append({
                        'coupon': coupon,
                        'deduct_amt': pledge_amt,
                        'final_pay': order_amt - pledge_amt,
                        'name': coupon_name
                    })
            elif coupon_type == '2':  # 折扣券
                # 对于折扣券，计算折扣后的金额
                discount_rate = 100
                for dimension in coupon.get('dimensionList', []):
                    if dimension.get('dimensionKey') == 'rebate':
                        for record in dimension.get('dimensionRecordList', []):
                            if record.get('dimensionValue'):
                                discount_rate = int(record['dimensionValue'])
                                break

                if discount_rate < 100:
                    discounted_amt = order_amt * discount_rate // 100
                    deduct_amt = order_amt - discounted_amt
                    usable_coupons.append({
                        'coupon': coupon,
                        'deduct_amt': deduct_amt,
                        'final_pay': discounted_amt,
                        'name': f"{coupon_name}({discount_rate}折)"
                    })

    if not usable_coupons:
        print("没有找到可用的优惠券")
        return None

    # 智能选择策略：优先选择最接近订单金额的优惠券，避免浪费大额券
    # 1. 首先查找能完全覆盖订单金额的券中，面额最小的（最接近订单金额的）
    # 2. 如果没有能完全覆盖的，则选择抵扣金额最大的

    # 分类：能完全覆盖订单的券 vs 不能完全覆盖的券
    full_coverage_coupons = [c for c in usable_coupons if c['final_pay'] == 0]
    partial_coverage_coupons = [c for c in usable_coupons if c['final_pay'] > 0]

    if full_coverage_coupons:
        # 有能完全覆盖的券，选择抵扣金额最小的（最接近订单金额，避免浪费）
        best_coupon = min(full_coverage_coupons, key=lambda x: x['deduct_amt'])
        print(f"🎯 智能选择优惠券: {best_coupon['name']} (精确匹配策略)")
    else:
        # 没有能完全覆盖的券，选择抵扣金额最大的（让用户支付最少）
        best_coupon = max(partial_coverage_coupons, key=lambda x: x['deduct_amt'])
        print(f"🎯 智能选择优惠券: {best_coupon['name']} (最大抵扣策略)")

    print(f"📊 订单金额: {order_amt/100:.2f}元")
    print(f"💰 优惠抵扣: {best_coupon['deduct_amt']/100:.2f}元")
    print(f"💳 需要支付: {best_coupon['final_pay']/100:.2f}元")

    return best_coupon

def generate_deduct_chksum(waybill_no, waybill_type, deduct_id, deduct_amt, deduct_type, deduct_by, point_val, point_no, pledge_amt, coupon_use_type):
    """生成deduct级别的chkSum"""
    import hashlib

    # 构造签名字符串
    sign_str = f"waybillNo={waybill_no}&waybillType={waybill_type}&deductId={deduct_id}&deductAmt={deduct_amt}&deductType={deduct_type}&deductBy={deduct_by}&pointVal={point_val}&pointNo={point_no}&pledgeAmt={pledge_amt}&couponUseType={coupon_use_type}"

    # MD5哈希并转大写
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()

def generate_root_chksum(re_pay, uuid, pay_product_id, pay_product_amt, pay_channel_type, gift_card_code, gift_card_amt, pay_card_type, mem_no):
    """生成根级别的chkSum"""
    import hashlib

    # 构造签名字符串
    sign_str = f"rePay={re_pay}&uuid={uuid}&payProductId={pay_product_id}&payProductAmt={pay_product_amt}&payChannelType={pay_channel_type}&giftCardCode={gift_card_code}&giftCardAmt={gift_card_amt}&payCardType={pay_card_type}&memNo={mem_no}"

    # MD5哈希并转大写
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()

def auto_pay_order(uuid, order_info, selected_coupon_info=None):
    """自动支付订单"""
    try:
        # 确保UUID有APP前缀
        if not uuid.startswith('APP'):
            uuid = f"APP{uuid}"

        # 从订单信息中提取支付所需的数据
        waybill = order_info['obj']['sfpayReqWaybill'][0]

        # 如果提供了选择的优惠券信息，使用它；否则使用默认的第一个
        if selected_coupon_info:
            # 从waybill的coupons中找到对应的优惠券详细信息
            selected_coupon_id = selected_coupon_info['coupon']['couponId']
            coupon = None
            for c in waybill['accountInfo']['coupons']:
                if c['couponId'] == selected_coupon_id:
                    coupon = c
                    break
            if not coupon:
                print(f"警告：未找到选择的优惠券 {selected_coupon_id}，使用默认优惠券")
                coupon = waybill['accountInfo']['coupons'][0]
        else:
            coupon = waybill['accountInfo']['coupons'][0]

        # 生成deduct级别的chkSum
        deduct_chksum = generate_deduct_chksum(
            waybill_no=waybill['waybillno'],
            waybill_type=waybill['waybilltype'],
            deduct_id=coupon['couponNo'],
            deduct_amt=waybill['amt'],
            deduct_type=1,
            deduct_by="2",
            point_val=0,
            point_no="",
            pledge_amt=coupon.get('pledgeAmt', waybill['amt']),
            coupon_use_type="1"
        )

        # 生成根级别的chkSum
        root_chksum = generate_root_chksum(
            re_pay="false",
            uuid=uuid,
            pay_product_id="",
            pay_product_amt="",
            pay_channel_type="WXPAY",
            gift_card_code="",
            gift_card_amt="",
            pay_card_type="",
            mem_no=""
        )

        pay_data = {
            "uuid": uuid,
            "deducts": [{
                "couponSource": "1",
                "encrypt": coupon['encrypt'],
                "pledgeAmt": coupon.get('pledgeAmt', waybill['amt']),
                "couponUseType": "1",
                "waybillType": waybill['waybilltype'],
                "waybillNo": waybill['waybillno'],
                "deductId": coupon['couponNo'],
                "couponFeeType": "1",
                "deductAmt": waybill['amt'],
                "monthlyDeductAmt": 0,
                "deductBy": "2",
                "deductType": 1,
                "pointVal": 0,
                "pointNo": "",
                "chkSum": deduct_chksum
            }],
            "memNo": "",
            "rePay": False,
            "giftCardCode": "",
            "giftCardAmt": "",
            "payChannelType": "WXPAY",
            "chkSum": root_chksum
        }

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0",
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json",
            "Referer": "https://sfpay.sf-express.com/nextpay/html/newcoupon/index88.html",
            "uuid": uuid
        }

        url = f"https://sfpay.sf-express.com/nextpay/coupons/pay?uuid={uuid}"

        print("--- 自动支付请求 ---")
        print(f"请求URL: {url}")
        print(f"请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
        print(f"请求数据: {json.dumps(pay_data, indent=2, ensure_ascii=False)}")

        response = requests.post(url, headers=headers, json=pay_data, verify=False)

        print("--- 自动支付响应 ---")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            return response.json()
        return None
    except Exception as e:
        print(f"自动支付失败: {e}")
        return None

def generate_random_device_id():
    """生成随机的device_id，格式类似UUID"""
    import uuid
    import random

    # 生成一个随机UUID并转换为字符串格式
    random_uuid = str(uuid.uuid4())

    # 或者基于原版格式生成随机device_id
    # 原版格式: c441a4d1-01bd-3b14-8b18-dca0146e826b
    # 保持相同的格式，但使用随机数据
    parts = [
        ''.join(random.choices('0123456789abcdef', k=8)),
        ''.join(random.choices('0123456789abcdef', k=4)),
        ''.join(random.choices('0123456789abcdef', k=4)),
        ''.join(random.choices('0123456789abcdef', k=4)),
        ''.join(random.choices('0123456789abcdef', k=12))
    ]
    return '-'.join(parts)

def send_sf_request(new_id, memberId, mobile, cookies_str):
    # 生成随机device_id
    device_id = generate_random_device_id()

    # 使用当前时间戳（毫秒）
    time_interval = str(int(time.time() * 1000))

    client_version = "9.68.2"
    encrypted_value = "2NBF+BE4{@P:@X${Q9BAE>{PAK!D:N*^"
    region_code = "CN"
    language_code = "sc"
    jsbundle = "d96859dfa30c20f5ab00c6c862c91c62"

    print(f"--- 随机生成的参数 ---")
    print(f"Device ID: {device_id}")
    print(f"Time Interval: {time_interval}")
    print(f"当前时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(time_interval)/1000))}")
    request_data = {
        "isDiscount": "N",
        "memberId": memberId,
        "mobile": mobile,
        "id": new_id,
        "additionInfo": [],
        "serviceName": "APP-PAY-QR",
        "serviceVersion": "V2.0.0",
        "appVersion": 1096801
    }
    request_body_json = json.dumps(request_data, separators=(',', ':'))

    def md5(text):
        import hashlib
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def generate_syt_token(device_id, time_interval, client_version, encrypted_value,
                          region_code, language_code, request_body, secret_key):
        request_body_md5 = md5(request_body + "&080R3MAC57J2{A19!$3:WO{I<1N$31BI")
        first_md5_input = f"{device_id}{time_interval}{client_version}{encrypted_value}{region_code}{language_code}{request_body_md5}{secret_key}&{encrypted_value}"
        first_md5 = md5(first_md5_input)
        final_md5_input = f"{first_md5}&0HQ%H91K&AA{{DH$*XV>XR)VKL:QFE{{&%"
        final_md5 = md5(final_md5_input)
        return final_md5

    syt_token = generate_syt_token(
        device_id, time_interval, client_version, encrypted_value,
        region_code, language_code, request_body_json, jsbundle
    )

    headers = {
        "jsbundle": jsbundle,
        "srcDeviceGuid": "DUFskPSn9YfFNSZyszabAOIZW-9uxJnWsc51",
        "clientVersion": client_version,
        "languageCode": language_code,
        "systemVersion": "13",
        "deviceId": device_id,
        "regionCode": region_code,
        "carrier": "unknown",
        "screenSize": "1080x2400",
        "sytToken": syt_token,
        "timeInterval": time_interval,
        "model": "M2012K10C",
        "mediaCode": "AndroidML",
        "memberId": request_data["memberId"],
        "Content-Type": "application/json",
        "User-Agent": "okhttp/4.9.1",
        "Host": "ccsp-egmas.sf-express.com",
        "Cookie": cookies_str
    }

    url = "https://ccsp-egmas.sf-express.com/cx-app-query/query/app/sfpay/applyPay"
    try:
        response = requests.post(url, headers=headers, data=request_body_json, verify=False)
        print("--- 服务器响应 ---")
        print(f"状态码: {response.status_code}")
        print(f"响应内容:\n{response.text}")
        return response.text
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

def get_image_from_clipboard():
    clip = ImageGrab.grabclipboard()
    # 1. 剪切板是图片
    if isinstance(clip, Image.Image):
        return clip
    # 2. 剪切板是文件路径或file:///路径
    elif isinstance(clip, list) and len(clip) > 0:
        path = clip[0]
        if path.startswith("file:///"):
            # 处理file:///路径和URL编码
            path = unquote(urlparse(path).path)
            if os.name == "nt" and path.startswith("/"):
                path = path[1:]  # Windows下去掉开头的斜杠
        if os.path.isfile(path):
            try:
                return Image.open(path)
            except Exception as e:
                print(f"图片文件打开失败: {e}")
    # 3. 剪切板是字符串形式的file:///路径
    elif isinstance(clip, str) and clip.startswith("file:///"):
        path = unquote(urlparse(clip).path)
        if os.name == "nt" and path.startswith("/"):
            path = path[1:]
        if os.path.isfile(path):
            try:
                return Image.open(path)
            except Exception as e:
                print(f"图片文件打开失败: {e}")
    return None

def capture_screen():
    """截取整个屏幕"""
    return ImageGrab.grab()

def capture_region(bbox):
    """截取指定区域"""
    return ImageGrab.grab(bbox=bbox)

def find_window_by_process_name(process_name):
    """根据进程名查找窗口句柄"""
    def enum_windows_proc(hwnd, windows):
        if win32gui.IsWindowVisible(hwnd):
            try:
                _, pid = win32gui.GetWindowThreadProcessId(hwnd)
                process = psutil.Process(pid)
                if process.name().lower() == process_name.lower():
                    window_text = win32gui.GetWindowText(hwnd)
                    if window_text:  # 只返回有标题的窗口
                        windows.append((hwnd, window_text, pid))
            except (psutil.NoSuchProcess, psutil.AccessDenied, Exception):
                pass
        return True

    windows = []
    win32gui.EnumWindows(enum_windows_proc, windows)
    return windows

def capture_window(hwnd):
    """截取指定窗口的图像"""
    try:
        # 获取窗口位置和大小
        left, top, right, bottom = win32gui.GetWindowRect(hwnd)
        width = right - left
        height = bottom - top

        # 创建设备上下文
        hwndDC = win32gui.GetWindowDC(hwnd)
        mfcDC = win32ui.CreateDCFromHandle(hwndDC)
        saveDC = mfcDC.CreateCompatibleDC()

        # 创建位图对象
        saveBitMap = win32ui.CreateBitmap()
        saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
        saveDC.SelectObject(saveBitMap)

        # 截取窗口
        result = windll.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 3)

        if result:
            # 获取位图数据
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)

            # 转换为PIL图像
            img = Image.frombuffer(
                'RGB',
                (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                bmpstr, 'raw', 'BGRX', 0, 1
            )

            # 清理资源
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(hwnd, hwndDC)

            return img
        else:
            # 如果PrintWindow失败，使用屏幕截图方式
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(hwnd, hwndDC)

            # 获取窗口在屏幕上的位置
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            return ImageGrab.grab(bbox=(left, top, right, bottom))

    except Exception as e:
        print(f"窗口截图失败: {e}")
        return None

class RegionSelector:
    """区域选择器"""
    def __init__(self, callback):
        self.callback = callback
        self.start_x = None
        self.start_y = None
        self.end_x = None
        self.end_y = None
        self.selecting = False

    def start_selection(self):
        """开始选择区域"""
        # 创建全屏透明窗口
        self.root = tk.Toplevel()
        self.root.attributes('-fullscreen', True)
        self.root.attributes('-alpha', 0.3)
        self.root.attributes('-topmost', True)
        self.root.configure(bg='black')
        self.root.overrideredirect(True)

        # 创建画布
        self.canvas = tk.Canvas(self.root, highlightthickness=0)
        self.canvas.pack(fill='both', expand=True)

        # 绑定鼠标事件
        self.canvas.bind('<Button-1>', self.on_click)
        self.canvas.bind('<B1-Motion>', self.on_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_release)
        self.canvas.bind('<Escape>', self.cancel_selection)

        # 添加提示文字
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        self.canvas.create_text(screen_width//2, 50,
                               text="拖动鼠标选择监控区域，按ESC取消",
                               fill='white', font=('Arial', 16))

        self.canvas.focus_set()

    def on_click(self, event):
        """鼠标按下"""
        self.start_x = event.x
        self.start_y = event.y
        self.selecting = True

    def on_drag(self, event):
        """鼠标拖动"""
        if self.selecting:
            # 清除之前的矩形
            self.canvas.delete('selection')
            # 绘制新的选择矩形
            self.canvas.create_rectangle(
                self.start_x, self.start_y, event.x, event.y,
                outline='red', width=2, tags='selection'
            )

    def on_release(self, event):
        """鼠标释放"""
        if self.selecting:
            self.end_x = event.x
            self.end_y = event.y
            self.selecting = False

            # 确保坐标正确（左上角到右下角）
            x1 = min(self.start_x, self.end_x)
            y1 = min(self.start_y, self.end_y)
            x2 = max(self.start_x, self.end_x)
            y2 = max(self.start_y, self.end_y)

            # 检查选择区域大小
            if abs(x2 - x1) > 10 and abs(y2 - y1) > 10:
                bbox = (x1, y1, x2, y2)
                self.root.destroy()
                self.callback(bbox)
            else:
                self.cancel_selection()

    def cancel_selection(self, event=None):
        """取消选择"""
        self.root.destroy()
        self.callback(None)

def wait_for_phone_in_clipboard(timeout=30):
    """等待剪切板中出现手机号，返回手机号或None"""
    print(f"等待剪切板中的手机号（{timeout}秒超时）...")
    start_time = time.time()
    last_clipboard = ""

    while time.time() - start_time < timeout:
        try:
            current_clipboard = pyperclip.paste()
            if current_clipboard != last_clipboard:
                # 检查是否是手机号（11位数字）
                if re.match(r'^1[3-9]\d{9}$', current_clipboard.strip()):
                    print(f"检测到手机号: {current_clipboard.strip()}")
                    return current_clipboard.strip()
                last_clipboard = current_clipboard
        except Exception as e:
            print(f"读取剪切板失败: {e}")
        time.sleep(0.5)

    print("等待超时，未检测到手机号")
    return None

def handle_screenshot_and_process(app_instance, hwnd=None, bbox=None):
    """处理截屏和识别流程"""
    # 更新状态
    app_instance.update_status("正在截屏...")

    # 截取屏幕、窗口或指定区域
    if bbox:
        img = capture_region(bbox)
    elif hwnd:
        img = capture_window(hwnd)
        if not img:
            app_instance.update_status("窗口截图失败，使用全屏截图")
            img = capture_screen()
    else:
        img = capture_screen()

    if img:
        app_instance.update_status("截屏成功，正在识别二维码...")
        url = decode_qr_code_from_image(img)
        if url:
            app_instance.update_status(f"识别到二维码: {url[:50]}...")
            new_id = extract_id_from_url(url)
            if new_id:
                # 等待剪切板中的手机号
                app_instance.update_status("等待手机号输入...")
                phone = wait_for_phone_in_clipboard()
                if not phone:
                    app_instance.update_status("未检测到手机号")
                    return

                app_instance.update_status(f"检测到手机号: {phone}")

                # 查找手机号对应URL
                txt_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "手机号.txt")
                url_from_txt = get_url_by_phone(phone, txt_path)
                if not url_from_txt:
                    app_instance.update_status(f"手机号 {phone} 未找到对应URL")
                    return

                app_instance.update_status("正在处理请求...")
                # GET获取cookies和参数
                cookies_str, memberId, mobile = get_cookies_and_params_from_url(url_from_txt)
                # 发POST请求
                resp_text = send_sf_request(new_id, memberId, mobile, cookies_str)
                if resp_text:
                    # 检查是否订单已支付
                    if '"errorMessage":"订单已支付"' in resp_text:
                        app_instance.update_status("✅ 订单已支付，跳过此手机号")
                        # 记录支付结果
                        log_payment_result(phone, url_from_txt, "已支付")
                        return

                    match = re.search(r'"payH5Url"\s*:\s*"([^"]+)"', resp_text)
                    if match:
                        pay_url = match.group(1)

                        # 提取UUID用于智能支付判断
                        uuid_match = re.search(r'id=([^&]+)', pay_url)
                        if uuid_match and app_instance.auto_pay_enabled.get():
                            uuid = uuid_match.group(1)
                            app_instance.update_status("正在获取订单信息...")

                            # 获取订单信息 
                            order_info = get_order_info(uuid)
                            if order_info and order_info.get('success'):
                                try:
                                    # 提取订单金额
                                    waybill = order_info['obj']['sfpayReqWaybill'][0]
                                    order_amt = waybill['amt']  # 订单金额（分）

                                    # 智能选择最优优惠券
                                    coupons = order_info['obj'].get('couponTemplateList', [])
                                    best_coupon_info = select_best_coupon(order_amt, coupons)

                                    if best_coupon_info:
                                        coupon = best_coupon_info['coupon']
                                        actual_deduct_amt = best_coupon_info['deduct_amt']
                                        need_pay_amt = best_coupon_info['final_pay']
                                        coupon_name = best_coupon_info['name']

                                        app_instance.update_status(f"订单金额: {order_amt/100:.2f}元, 优惠券: {coupon_name}, 可抵扣: {actual_deduct_amt/100:.2f}元")
                                    else:
                                        # 没有可用优惠券
                                        need_pay_amt = order_amt
                                        app_instance.update_status(f"订单金额: {order_amt/100:.2f}元, 无可用优惠券")

                                        # 判断是否自动支付（只有完全免费时才自动支付）
                                        if need_pay_amt <= 0:
                                            app_instance.update_status("完全免费，等待5秒后尝试自动支付...")
                                            # 等待5秒避免频率限制
                                            time.sleep(5)
                                            app_instance.update_status("正在尝试自动支付...")
                                            pay_result = auto_pay_order(uuid, order_info, best_coupon_info)
                                            if pay_result and pay_result.get('success'):
                                                app_instance.update_status("✅ 自动支付成功，继续监控")
                                                # 记录支付结果
                                                log_payment_result(phone, pay_url, "自动支付成功")
                                                return
                                            else:
                                                # 检查是否是频率限制错误
                                                if pay_result and "操作过于频繁" in str(pay_result.get('errorMessage', '')):
                                                    app_instance.update_status("⏰ 操作过于频繁，等待10秒后重试...")
                                                    time.sleep(10)
                                                    app_instance.update_status("正在重试自动支付...")
                                                    pay_result = auto_pay_order(uuid, order_info, best_coupon_info)
                                                    if pay_result and pay_result.get('success'):
                                                        app_instance.update_status("✅ 自动支付成功，继续监控")
                                                        log_payment_result(phone, pay_url, "自动支付成功")
                                                        return

                                                app_instance.update_status("❌ 自动支付失败，打开支付页面")
                                                # 记录支付失败
                                                log_payment_result(phone, pay_url, "支付失败")
                                        else:
                                            app_instance.update_status(f"需支付: {need_pay_amt/100:.2f}元，打开支付页面")
                                            # 记录需要手动支付
                                            log_payment_result(phone, pay_url, "需要手动支付", need_pay_amt)
                                    else:
                                        app_instance.update_status("未找到优惠券信息，打开支付页面")
                                        # 记录需要手动支付（无优惠券）
                                        try:
                                            waybill = order_info['obj']['sfpayReqWaybill'][0]
                                            order_amt = waybill['amt']
                                            log_payment_result(phone, pay_url, "需要手动支付", order_amt)
                                        except:
                                            log_payment_result(phone, pay_url, "需要手动支付")
                                except Exception as e:
                                    app_instance.update_status(f"解析订单信息失败: {e}")
                            else:
                                app_instance.update_status("获取订单信息失败，打开支付页面")

                        # 显示支付二维码
                        show_qr_code_window(pay_url, phone)
                        if not app_instance.auto_pay_enabled.get():
                            app_instance.update_status("✅ 支付二维码已显示")
                    else:
                        app_instance.update_status("❌ 未找到支付链接")
                else:
                    app_instance.update_status("❌ 请求失败")
            else:
                app_instance.update_status("❌ 未能从URL中提取到id参数")
        else:
            app_instance.update_status("❌ 未识别到二维码")
    else:
        app_instance.update_status("❌ 截屏失败")

class ScreenshotApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("顺丰二维码识别工具")
        self.root.geometry("400x300")
        self.root.resizable(False, False)

        # 设置窗口始终在最前面
        self.root.attributes('-topmost', True)

        # 监控相关变量
        self.monitoring = False
        self.target_hwnd = None
        self.monitor_bbox = None
        self.last_qr_hash = None
        self.processed_qr_codes = set()  # 记录已处理的二维码内容

        # 智能支付开关
        self.auto_pay_enabled = tk.BooleanVar(value=True)

        # 窗口位置
        self.window_x = 100
        self.window_y = 100

        # 配置文件路径
        self.config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "monitor_config.pkl")

        # 加载配置
        self.load_config()

        # 创建界面
        self.create_widgets()

        # 设置窗口位置
        self.set_window_position()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_widgets(self):
        # 标题标签
        title_label = tk.Label(self.root, text="顺丰二维码识别工具",
                              font=("Arial", 14, "bold"))
        title_label.pack(pady=10)

        # 监控模式选择
        mode_frame = tk.Frame(self.root)
        mode_frame.pack(pady=5, padx=20, fill='x')

        tk.Label(mode_frame, text="监控模式:", font=("Arial", 10)).pack(side='left')
        self.mode_var = tk.StringVar(value="region")

        mode_radio1 = tk.Radiobutton(mode_frame, text="区域监控", variable=self.mode_var,
                                    value="region", font=("Arial", 9))
        mode_radio1.pack(side='left', padx=(10, 5))

        # 区域选择框架
        region_frame = tk.Frame(self.root)
        region_frame.pack(pady=5, padx=20, fill='x')

        # 区域选择按钮
        region_btn = tk.Button(region_frame, text="选择监控区域", command=self.select_region,
                              font=("Arial", 10, "bold"), bg="#FF9800", fg="white",
                              width=15, height=1)
        region_btn.pack(side='left')

        # 清除区域按钮
        clear_btn = tk.Button(region_frame, text="清除区域", command=self.clear_region,
                             font=("Arial", 8), bg="#9E9E9E", fg="white")
        clear_btn.pack(side='left', padx=(5, 0))

        # 清除二维码记录按钮
        clear_qr_btn = tk.Button(region_frame, text="清除记录", command=self.clear_qr_records,
                                font=("Arial", 8), bg="#607D8B", fg="white")
        clear_qr_btn.pack(side='left', padx=(5, 0))

        # 智能支付开关
        pay_frame = tk.Frame(self.root)
        pay_frame.pack(pady=5, padx=20, fill='x')

        auto_pay_check = tk.Checkbutton(pay_frame, text="智能支付（完全免费时自动支付）",
                                       variable=self.auto_pay_enabled,
                                       font=("Arial", 9),
                                       command=self.save_config)
        auto_pay_check.pack(side='left')

        # 区域信息显示
        self.region_label = tk.Label(self.root, text="未选择监控区域",
                                     font=("Arial", 9), fg="gray")
        self.region_label.pack(pady=2)

        # 更新区域显示
        self.update_region_display()

        # 状态显示
        self.status_label = tk.Label(self.root, text="就绪",
                                    font=("Arial", 10), fg="blue",
                                    wraplength=350, justify='left')
        self.status_label.pack(pady=10, padx=20)

        # 按钮框架
        btn_frame = tk.Frame(self.root)
        btn_frame.pack(pady=10)

        # 开始监控按钮
        self.monitor_btn = tk.Button(btn_frame, text="开始监控",
                                    command=self.toggle_monitoring,
                                    font=("Arial", 12, "bold"),
                                    bg="#4CAF50", fg="white",
                                    width=12, height=2)
        self.monitor_btn.pack(side='left', padx=5)

        # 手动截屏按钮
        screenshot_btn = tk.Button(btn_frame, text="手动截屏",
                                  command=self.on_screenshot_click,
                                  font=("Arial", 12, "bold"),
                                  bg="#2196F3", fg="white",
                                  width=12, height=2)
        screenshot_btn.pack(side='left', padx=5)

        # 退出按钮
        exit_btn = tk.Button(self.root, text="退出",
                            command=self.on_closing,
                            font=("Arial", 10),
                            bg="#f44336", fg="white",
                            width=10)
        exit_btn.pack(pady=10)

    def load_config(self):
        """加载配置"""
        try:
            with open(self.config_file, 'rb') as f:
                config = pickle.load(f)
                self.monitor_bbox = config.get('monitor_bbox')
                self.window_x = config.get('window_x', 100)
                self.window_y = config.get('window_y', 100)
                self.auto_pay_enabled.set(config.get('auto_pay_enabled', True))
        except (FileNotFoundError, Exception):
            self.monitor_bbox = None
            self.window_x = 100
            self.window_y = 100

    def save_config(self):
        """保存配置"""
        try:
            # 获取当前窗口位置
            self.window_x = self.root.winfo_x()
            self.window_y = self.root.winfo_y()

            config = {
                'monitor_bbox': self.monitor_bbox,
                'window_x': self.window_x,
                'window_y': self.window_y,
                'auto_pay_enabled': self.auto_pay_enabled.get()
            }
            with open(self.config_file, 'wb') as f:
                pickle.dump(config, f)
        except Exception as e:
            print(f"保存配置失败: {e}")

    def set_window_position(self):
        """设置窗口位置"""
        try:
            # 确保窗口位置在屏幕范围内
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()

            # 限制窗口位置
            if self.window_x < 0 or self.window_x > screen_width - 400:
                self.window_x = 100
            if self.window_y < 0 or self.window_y > screen_height - 300:
                self.window_y = 100

            self.root.geometry(f"400x300+{self.window_x}+{self.window_y}")
        except Exception as e:
            print(f"设置窗口位置失败: {e}")
            self.root.geometry("400x300+100+100")

    def update_region_display(self):
        """更新区域显示"""
        if self.monitor_bbox:
            x1, y1, x2, y2 = self.monitor_bbox
            self.region_label.config(
                text=f"监控区域: ({x1},{y1}) - ({x2},{y2}) [{x2-x1}x{y2-y1}]",
                fg="green"
            )
        else:
            self.region_label.config(text="未选择监控区域", fg="gray")

    def clear_region(self):
        """清除选择的区域"""
        self.monitor_bbox = None
        self.update_region_display()
        self.save_config()
        self.update_status("已清除监控区域")

    def clear_qr_records(self):
        """清除已处理的二维码记录"""
        count = len(self.processed_qr_codes)
        self.processed_qr_codes.clear()
        self.update_status(f"已清除二维码记录，共清除 {count} 条记录")

    def select_region(self):
        """选择监控区域"""
        self.update_status("请拖动鼠标选择监控区域...")
        selector = RegionSelector(self.on_region_selected)
        selector.start_selection()

    def on_region_selected(self, bbox):
        """区域选择完成回调"""
        if bbox:
            self.monitor_bbox = bbox
            self.update_region_display()
            self.save_config()
            self.update_status("区域选择完成并已保存")
        else:
            self.update_status("区域选择已取消")

    def toggle_monitoring(self):
        """切换监控状态"""
        if not self.monitoring:
            # 开始监控
            if not self.monitor_bbox:
                self.update_status("请先选择监控区域")
                return

            x1, y1, x2, y2 = self.monitor_bbox
            monitor_target = f"区域 ({x1},{y1})-({x2},{y2})"

            self.monitoring = True
            self.monitor_btn.config(text="停止监控", bg="#f44336")
            self.update_status(f"开始监控: {monitor_target}")
            self.start_monitoring()
        else:
            # 停止监控
            self.monitoring = False
            self.monitor_btn.config(text="开始监控", bg="#4CAF50")
            self.update_status("监控已停止")

    def start_monitoring(self):
        """开始监控循环"""
        if self.monitoring:
            try:
                if self.monitor_bbox:
                    # 区域监控模式
                    img = capture_region(self.monitor_bbox)

                    if img:
                        # 识别二维码
                        url = decode_qr_code_from_image(img)
                        if url:
                            # 检查是否已经处理过这个二维码
                            if url not in self.processed_qr_codes:
                                self.processed_qr_codes.add(url)
                                self.update_status(f"检测到新二维码: {url[:50]}...")
                                # 在新线程中处理，避免阻塞界面
                                threading.Thread(target=self.process_qr_code, args=(url,), daemon=True).start()
                            else:
                                # 已处理过的二维码，静默跳过，但显示监控状态
                                if not hasattr(self, '_last_status_update') or time.time() - self._last_status_update > 5:
                                    self.update_status("🔍 监控中...")
                                    self._last_status_update = time.time()
                        else:
                            # 没有检测到二维码，定期更新监控状态
                            if not hasattr(self, '_last_status_update') or time.time() - self._last_status_update > 5:
                                self.update_status("🔍 监控中...")
                                self._last_status_update = time.time()

            except Exception as e:
                self.update_status(f"监控出错: {e}")

            # 继续监控
            self.root.after(1000, self.start_monitoring)  # 每秒检查一次

    def process_qr_code(self, url):
        """处理检测到的二维码"""
        new_id = extract_id_from_url(url)
        if new_id:
            self.update_status("等待手机号输入...")
            phone = wait_for_phone_in_clipboard()
            if not phone:
                self.update_status("未检测到手机号")
                return

            self.update_status(f"检测到手机号: {phone}")

            # 查找手机号对应URL
            txt_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "手机号.txt")
            url_from_txt = get_url_by_phone(phone, txt_path)
            if not url_from_txt:
                self.update_status(f"手机号 {phone} 未找到对应URL")
                return

            self.update_status("正在处理请求...")
            # GET获取cookies和参数
            cookies_str, memberId, mobile = get_cookies_and_params_from_url(url_from_txt)
            # 发POST请求
            resp_text = send_sf_request(new_id, memberId, mobile, cookies_str)
            if resp_text:
                # 检查是否订单已支付
                if '"errorMessage":"订单已支付"' in resp_text:
                    self.update_status("✅ 订单已支付，跳过此手机号")
                    # 记录支付结果
                    log_payment_result(phone, url_from_txt, "已支付")
                    # 延迟后重置状态为监控中
                    self.root.after(2000, lambda: self.update_status("🔍 监控中..."))
                    return

                match = re.search(r'"payH5Url"\s*:\s*"([^"]+)"', resp_text)
                if match:
                    pay_url = match.group(1)

                    # 提取UUID用于智能支付判断
                    uuid_match = re.search(r'id=([^&]+)', pay_url)
                    if uuid_match and self.auto_pay_enabled.get():
                        uuid = uuid_match.group(1)
                        self.update_status("正在获取订单信息...")

                        # 获取订单信息
                        order_info = get_order_info(uuid)
                        if order_info and order_info.get('success'):
                            try:
                                # 提取订单金额
                                waybill = order_info['obj']['sfpayReqWaybill'][0]
                                order_amt = waybill['amt']  # 订单金额（分）

                                # 智能选择最优优惠券
                                coupons = order_info['obj'].get('couponTemplateList', [])
                                best_coupon_info = select_best_coupon(order_amt, coupons)

                                if best_coupon_info:
                                    coupon = best_coupon_info['coupon']
                                    actual_deduct_amt = best_coupon_info['deduct_amt']
                                    need_pay_amt = best_coupon_info['final_pay']
                                    coupon_name = best_coupon_info['name']

                                    self.update_status(f"订单金额: {order_amt/100:.2f}元, 优惠券: {coupon_name}, 可抵扣: {actual_deduct_amt/100:.2f}元")
                                else:
                                    # 没有可用优惠券
                                    need_pay_amt = order_amt
                                    self.update_status(f"订单金额: {order_amt/100:.2f}元, 无可用优惠券")

                                    # 判断是否自动支付（只有完全免费时才自动支付）
                                    if need_pay_amt <= 0:
                                        self.update_status("完全免费，等待5秒后尝试自动支付...")
                                        # 等待5秒避免频率限制
                                        time.sleep(5)
                                        self.update_status("正在尝试自动支付...")
                                        pay_result = auto_pay_order(uuid, order_info, best_coupon_info)
                                        if pay_result and pay_result.get('success'):
                                            self.update_status("✅ 自动支付成功，继续监控")
                                            # 记录支付结果
                                            log_payment_result(phone, pay_url, "自动支付成功")
                                            return
                                        else:
                                            # 检查是否是频率限制错误
                                            if pay_result and "操作过于频繁" in str(pay_result.get('errorMessage', '')):
                                                self.update_status("⏰ 操作过于频繁，等待10秒后重试...")
                                                time.sleep(10)
                                                self.update_status("正在重试自动支付...")
                                                pay_result = auto_pay_order(uuid, order_info, best_coupon_info)
                                                if pay_result and pay_result.get('success'):
                                                    self.update_status("✅ 自动支付成功，继续监控")
                                                    log_payment_result(phone, pay_url, "自动支付成功")
                                                    return

                                            self.update_status("❌ 自动支付失败，打开支付页面")
                                            # 记录支付失败
                                            log_payment_result(phone, pay_url, "支付失败")
                                    else:
                                        self.update_status(f"需支付: {need_pay_amt/100:.2f}元，打开支付页面")
                                        # 记录需要手动支付
                                        log_payment_result(phone, pay_url, "需要手动支付", need_pay_amt)
                                else:
                                    self.update_status("未找到优惠券信息，打开支付页面")
                                    # 记录需要手动支付（无优惠券）
                                    try:
                                        waybill = order_info['obj']['sfpayReqWaybill'][0]
                                        order_amt = waybill['amt']
                                        log_payment_result(phone, pay_url, "需要手动支付", order_amt)
                                    except:
                                        log_payment_result(phone, pay_url, "需要手动支付")
                            except Exception as e:
                                self.update_status(f"解析订单信息失败: {e}")
                        else:
                            self.update_status("获取订单信息失败，打开支付页面")

                    # 显示支付二维码
                    show_qr_code_window(pay_url, phone)
                    if not self.auto_pay_enabled.get():
                        self.update_status("✅ 支付二维码已显示")
                else:
                    self.update_status("❌ 未找到支付链接")
            else:
                self.update_status("❌ 请求失败")
        else:
            self.update_status("❌ 未能从URL中提取到id参数")

    def update_status(self, message):
        """更新状态显示"""
        self.status_label.config(text=message)
        print(message)

    def on_screenshot_click(self):
        """手动截屏按钮点击事件"""
        if not self.monitor_bbox:
            self.update_status("请先选择监控区域")
            return

        # 在新线程中处理，避免阻塞界面
        threading.Thread(target=handle_screenshot_and_process, args=(self, None, self.monitor_bbox), daemon=True).start()

    def on_closing(self):
        """窗口关闭事件"""
        self.save_config()
        self.root.destroy()

    def run(self):
        """运行应用"""
        self.root.mainloop()

def main():
    import warnings
    warnings.filterwarnings("ignore")

    print("启动顺丰二维码识别工具...")

    # 创建并运行GUI应用
    app = ScreenshotApp()
    app.run()

if __name__ == "__main__":
    main()