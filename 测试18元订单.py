"""
测试18元订单的优惠券选择
"""

# 模拟你提供的真实数据
order_amt = 1800  # 18元订单
coupons = [
    {
        "couponFeeType": "1",
        "couponId": "377946",
        "couponSource": "1",
        "couponType": "1",
        "couponTypeName": "20元免单券",
        "couponUseType": "1",
        "dimensionList": [],
        "pledgeAmt": 2000
    },
    {
        "couponFeeType": "1",
        "couponId": "379541",
        "couponSource": "1",
        "couponType": "1",
        "couponTypeName": "2元通用运费券",
        "couponUseType": "1",
        "dimensionList": [],
        "pledgeAmt": 200
    }
]

def test_coupon_selection(order_amt, coupons):
    print(f"🔍 测试订单金额: {order_amt}分 ({order_amt/100:.2f}元)")
    print(f"📋 可选优惠券:")
    
    usable_coupons = []
    for i, coupon in enumerate(coupons):
        pledge_amt = coupon.get('pledgeAmt', 0)
        coupon_type = coupon.get('couponType', '')
        coupon_name = coupon.get('couponTypeName', '未知优惠券')
        
        print(f"   券{i+1}: {coupon_name} (类型:{coupon_type}, 面额:{pledge_amt}分)")
        
        if coupon_type == '1':  # 免单券
            if pledge_amt <= order_amt:  # 抵扣金额不超过订单金额
                print(f"      ✅ 可用: {pledge_amt} <= {order_amt}")
                usable_coupons.append({
                    'coupon': coupon,
                    'deduct_amt': pledge_amt,
                    'final_pay': order_amt - pledge_amt,
                    'name': coupon_name
                })
            else:
                print(f"      ❌ 不可用: {pledge_amt} > {order_amt} (券面额超过订单金额)")
    
    print(f"\n📊 筛选结果: 找到 {len(usable_coupons)} 个可用优惠券")
    
    if usable_coupons:
        # 智能选择策略
        full_coverage = [c for c in usable_coupons if c['final_pay'] == 0]
        partial_coverage = [c for c in usable_coupons if c['final_pay'] > 0]
        
        if full_coverage:
            best = min(full_coverage, key=lambda x: x['deduct_amt'])
            strategy = "精确匹配"
        else:
            best = max(partial_coverage, key=lambda x: x['deduct_amt'])
            strategy = "最大抵扣"
        
        print(f"🎯 智能选择: {best['name']} ({strategy})")
        print(f"💰 优惠抵扣: {best['deduct_amt']/100:.2f}元")
        print(f"💳 需要支付: {best['final_pay']/100:.2f}元")
        
        return best
    else:
        print("❌ 没有可用优惠券")
        return None

if __name__ == "__main__":
    print("=" * 50)
    print("测试18元订单的优惠券选择逻辑")
    print("=" * 50)
    
    result = test_coupon_selection(order_amt, coupons)
    
    print("\n" + "=" * 50)
    print("预期结果:")
    print("- 20元券: 不可用 (20 > 18)")
    print("- 2元券: 可用 (2 <= 18)")
    print("- 应该选择: 2元券")
    print("- 支付金额: 18 - 2 = 16元")
    
    if result:
        print(f"\n✅ 测试通过! 选择了 {result['name']}")
    else:
        print(f"\n❌ 测试失败! 没有选择任何券")
